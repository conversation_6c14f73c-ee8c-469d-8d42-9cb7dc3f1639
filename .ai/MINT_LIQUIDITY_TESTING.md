# MintLiquidity Fork Testing Guide

This guide explains how to test the `mintLiquidity` function using fork tests against the real SEI mainnet.

## Overview

The `MintLiquidityForkTest` contract provides comprehensive tests for the `mintLiquidity` function using real SEI mainnet pools and tokens. These tests validate that your implementation works correctly with actual on-chain data.

## Test Files

- `test/MintLiquidityFork.t.sol` - Main fork test contract
- `test_mint_liquidity.sh` - Test runner script
- `examples/MintLiquidityExample.sol` - Usage examples

## Real Addresses Used

### Tokens
- **USDC**: `0x3894085Ef7Ff0f0aeDf52E2A2704928d1Ec074F1`
- **WSEI**: `0xE30feDd158A2e3b13e9badaeABaFc5516e95e8C7`

### Pools
- **USDC/WSEI Pool**: `0x882f62fe8E9594470D1da0f70Bc85096F6c60423`

## Running the Tests

### Quick Start
```bash
# Make script executable (if not already)
chmod +x test_mint_liquidity.sh

# Run all tests
./test_mint_liquidity.sh
```

### Individual Tests
```bash
# Basic functionality test
forge test --match-contract MintLiquidityForkTest --match-test testMintLiquidityBasic --fork-url https://evm-rpc.sei-apis.com -vvv

# Different tick ranges test
forge test --match-contract MintLiquidityForkTest --match-test testMintLiquidityDifferentRanges --fork-url https://evm-rpc.sei-apis.com -vvv

# Edge cases test
forge test --match-contract MintLiquidityForkTest --match-test testMintLiquidityEdgeCases --fork-url https://evm-rpc.sei-apis.com -vvv

# Real whales test
forge test --match-contract MintLiquidityForkTest --match-test testMintLiquidityWithRealWhales --fork-url https://evm-rpc.sei-apis.com -vvv
```

### All Tests
```bash
forge test --match-contract MintLiquidityForkTest --fork-url https://evm-rpc.sei-apis.com -vvv
```

## Test Scenarios

### 1. Basic Mint Liquidity (`testMintLiquidityBasic`)
- Tests basic `mintLiquidity` functionality
- Uses full range positions (-887220 to 887220)
- Verifies token usage and balance changes
- Validates return values

### 2. Different Tick Ranges (`testMintLiquidityDifferentRanges`)
- Tests various tick ranges:
  - Concentrated liquidity (tight range around current price)
  - Wide range positions
  - Positions below current price (USDC-only)
  - Positions above current price (WSEI-only)

### 3. Edge Cases (`testMintLiquidityEdgeCases`)
- Invalid pool address
- Invalid tick ranges (tickLower >= tickUpper)
- Zero amounts for both tokens
- Validates proper error handling

### 4. Real Whales (`testMintLiquidityWithRealWhales`)
- Attempts to find real whale addresses
- Transfers tokens from whales to test user
- Tests with real token balances

## Token Funding Strategies

The tests use multiple strategies to fund the test user with tokens:

### 1. `deal()` Function
```solidity
deal(USDC, TEST_USER, 1000 * 1e6); // 1000 USDC
deal(WSEI, TEST_USER, 1000 * 1e18); // 1000 WSEI
```

### 2. Storage Manipulation
```solidity
function _setTokenBalance(address token, address user, uint256 amount) internal {
    bytes32 slot = keccak256(abi.encode(user, uint256(0)));
    vm.store(token, slot, bytes32(amount));
}
```

### 3. Whale Transfers
```solidity
vm.prank(whaleAddress);
IERC20(token).transfer(testUser, amount);
```

## Expected Results

### Successful Test Output
```
✅ Funded TEST_USER with tokens using deal()
✅ Mint successful!
Amount0 (USDC) used: 1000000
Amount1 (WSEI) used: 500000000000000000
Final USDC balance: 999000000
Final WSEI balance: 999500000000000000000
```

### Common Issues and Solutions

#### Issue: "Could not fund tokens"
**Solution**: The `deal()` function might not work for all tokens. Try:
1. Finding real whale addresses using blockchain explorers
2. Using storage manipulation
3. Using different token amounts

#### Issue: "Mint failed with reason: ..."
**Possible causes**:
- Insufficient token approvals
- Invalid tick ranges
- Pool not initialized
- Slippage issues

**Solution**: Check the error message and verify:
- Token balances and approvals
- Tick range validity
- Pool state

#### Issue: "Pool state could not be read"
**Solution**: Verify:
- Pool address is correct
- Fork URL is working
- Pool is properly initialized

## Customizing Tests

### Adding New Test Scenarios
```solidity
function testCustomScenario() public {
    // Your custom test logic here
    console.log("Testing custom scenario");
    
    // Setup
    uint256 customAmount0 = 500 * 1e6;  // 500 USDC
    uint256 customAmount1 = 500 * 1e18; // 500 WSEI
    
    // Test
    vm.startPrank(TEST_USER);
    // ... test logic
    vm.stopPrank();
}
```

### Using Different Pools
```solidity
// Add new pool addresses
address constant WBTC_USDC_POOL = ******************************************;

// Test with different pool
liquidityManager.mintLiquidity(
    amount0Max,
    amount1Max,
    tickLower,
    tickUpper,
    WBTC_USDC_POOL, // Different pool
    recipient
);
```

## Debugging Tips

1. **Use verbose output**: Add `-vvv` flag for detailed logs
2. **Check balances**: Log token balances before and after operations
3. **Verify approvals**: Ensure tokens are properly approved
4. **Check pool state**: Read `slot0()` to understand current pool state
5. **Use console.log**: Add logging throughout your tests

## Integration with CI/CD

Add to your GitHub Actions or other CI/CD:

```yaml
- name: Run Fork Tests
  run: |
    cd contracts
    forge test --match-contract MintLiquidityForkTest --fork-url https://evm-rpc.sei-apis.com
```

## Next Steps

After running these tests successfully:

1. **Verify gas usage**: Check gas consumption for different scenarios
2. **Test edge cases**: Add more edge case scenarios
3. **Performance testing**: Test with larger amounts
4. **Multi-pool testing**: Test with different pools
5. **Integration testing**: Test with your frontend/backend integration

## Support

If you encounter issues:
1. Check the console output for detailed error messages
2. Verify all addresses are correct for SEI mainnet
3. Ensure your RPC endpoint is working
4. Check that the pool has sufficient liquidity

For more examples, see `examples/MintLiquidityExample.sol`.
